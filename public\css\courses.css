/* Courses Page Styles - Matching Main Theme */

/* Variables */
:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary-color: #10b981;
  --accent-color: #f59e0b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #3b82f6;
  
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  --white: #ffffff;
  --black: #000000;
  
  --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  --border-radius: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  --transition: all 0.3s ease;
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--gray-50);
  direction: rtl;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Navigation */
.navbar {
  background: var(--gray-900);
  box-shadow: var(--shadow-sm);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 1rem 0;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

.navbar-brand:hover {
  color: var(--primary-dark);
}

.navbar-brand i {
  font-size: 1.5rem;
}

.nav-links {
  display: flex;
  list-style: none;
  gap: 2rem;
  align-items: center;
}

.nav-links li {
  list-style: none;
}

.nav-link {
  color: var(--white);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
  background-color: var(--gray-100);
}

.nav-cta {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.user-menu {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.user-name {
  color: var(--gray-700);
  font-weight: 600;
  font-size: var(--font-size-base);
}

.mobile-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 0.25rem;
}

.mobile-toggle span {
  width: 25px;
  height: 3px;
  background: var(--gray-700);
  border-radius: 2px;
  transition: var(--transition);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  white-space: nowrap;
}

.btn-primary {
  background: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--gray-600);
  color: var(--white);
}

.btn-secondary:hover {
  background: var(--gray-700);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: var(--white);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  padding: 8rem 0 4rem;
  color: var(--white);
  text-align: center;
  margin-top: 80px;
}

.hero-content h1 {
  font-size: var(--font-size-4xl);
  font-weight: 800;
  margin-bottom: 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-content p {
  font-size: var(--font-size-xl);
  margin-bottom: 3rem;
  opacity: 0.95;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Courses Filters */
.courses-filters {
  max-width: 900px;
  margin: 0 auto;
}

.search-container {
  display: flex;
  margin-bottom: 2rem;
  background: var(--white);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.search-container input {
  flex: 1;
  padding: 1.25rem 1.5rem;
  border: none;
  font-size: var(--font-size-lg);
  color: var(--gray-700);
  background: transparent;
}

.search-container input:focus {
  outline: none;
}

.search-container input::placeholder {
  color: var(--gray-400);
}

.search-btn {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  padding: 1.25rem 2rem;
  cursor: pointer;
  transition: var(--transition);
  font-size: var(--font-size-lg);
}

.search-btn:hover {
  background: var(--primary-dark);
}

.filter-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.filter-container select {
  padding: 1rem 1.5rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius);
  background: rgba(255, 255, 255, 0.15);
  color: var(--white);
  font-size: var(--font-size-base);
  backdrop-filter: blur(10px);
  cursor: pointer;
}

.filter-container select option {
  background: var(--gray-800);
  color: var(--white);
}

/* Courses Section */
.courses-section {
  padding: 4rem 0;
  background: var(--gray-50);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.results-info {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
  font-weight: 600;
}

.sort-container select {
  padding: 0.75rem 1rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  background: var(--white);
  font-size: var(--font-size-base);
  color: var(--gray-700);
  cursor: pointer;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rem 0;
  color: var(--gray-500);
}

.loading-state .spinner {
  width: 50px;
  height: 50px;
  border: 4px solid var(--gray-200);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  font-size: var(--font-size-lg);
  font-weight: 500;
}

/* Courses Grid */
.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

/* Course Card */
.course-card {
  background: var(--white);
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: var(--transition);
  cursor: pointer;
  height: fit-content;
  border: 1px solid var(--gray-200);
}

.course-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-color);
}

.course-image {
  position: relative;
  height: 220px;
  overflow: hidden;
  background: var(--gray-100);
}

.course-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.course-card:hover .course-image img {
  transform: scale(1.05);
}

.course-level {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.8);
  color: var(--white);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.course-price {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  background: var(--primary-color);
  color: var(--white);
  padding: 0.75rem 1.25rem;
  border-radius: var(--border-radius);
  font-weight: 700;
  font-size: var(--font-size-base);
  box-shadow: var(--shadow-md);
}

.course-content {
  padding: 2rem;
}

.course-category {
  color: var(--primary-color);
  font-size: var(--font-size-sm);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.75rem;
  display: inline-block;
  background: var(--gray-100);
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius);
}

.course-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 1rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-description {
  color: var(--gray-600);
  font-size: var(--font-size-base);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-instructor {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--gray-600);
  font-size: var(--font-size-base);
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.course-instructor i {
  color: var(--primary-color);
  font-size: var(--font-size-lg);
}

.course-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--gray-500);
  padding-top: 1rem;
  border-top: 1px solid var(--gray-200);
}

.course-rating,
.course-students,
.course-videos {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.course-rating i {
  color: var(--warning-color);
}

.course-students i,
.course-videos i {
  color: var(--primary-color);
}

/* No Results */
.no-results {
  text-align: center;
  padding: 6rem 0;
  color: var(--gray-500);
}

.no-results i {
  font-size: 5rem;
  margin-bottom: 2rem;
  color: var(--gray-300);
}

.no-results h3 {
  font-size: var(--font-size-2xl);
  margin-bottom: 1rem;
  color: var(--gray-600);
  font-weight: 600;
}

.no-results p {
  font-size: var(--font-size-lg);
  color: var(--gray-500);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 4rem;
}

.pagination-controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.pagination-btn {
  padding: 1rem 1.25rem;
  border: 1px solid var(--gray-300);
  background: var(--white);
  color: var(--gray-700);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 600;
  min-width: 50px;
}

.pagination-btn:hover {
  background: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.pagination-btn.active {
  background: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.pagination-dots {
  padding: 1rem 0.75rem;
  color: var(--gray-400);
  font-weight: 600;
}

/* Footer */
.footer {
  background: var(--gray-900);
  color: var(--gray-300);
  padding: 4rem 0 2rem;
  margin-top: 4rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 3rem;
  margin-bottom: 2rem;
}

.footer-section h4 {
  color: var(--white);
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.footer-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.footer-brand i {
  font-size: 1.5rem;
}

.footer-section p {
  line-height: 1.6;
  margin-bottom: 1rem;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 0.75rem;
}

.footer-section ul li a {
  color: var(--gray-300);
  text-decoration: none;
  transition: var(--transition);
}

.footer-section ul li a:hover {
  color: var(--primary-color);
}

.contact-info p {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.contact-info i {
  color: var(--primary-color);
  width: 20px;
}

.social-links {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  background: var(--gray-800);
  color: var(--gray-300);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.social-links a:hover {
  background: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid var(--gray-800);
  color: var(--gray-400);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .courses-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
  }

  .hero-content h1 {
    font-size: var(--font-size-3xl);
  }

  .hero-content p {
    font-size: var(--font-size-lg);
  }
}

@media (max-width: 768px) {
 
  .nav-links {
    display: none;
  }

  .mobile-toggle {
    display: flex;  
  }

  .user-menu {
    flex-direction: column;
  }
 

  .user-info {
    gap: 0.5rem;
  }

  .user-name {
    font-size: var(--font-size-sm);
  }

  .hero-section {
    padding: 6rem 0 3rem;
  }

  .hero-content h1 {
    font-size: var(--font-size-2xl);
  }

  .hero-content p {
    font-size: var(--font-size-base);
    margin-bottom: 2rem;
  }

  .filter-container {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .courses-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .course-meta {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .social-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.75rem;
  }

  .hero-section {
    padding: 5rem 0 2rem;
  }

  .hero-content h1 {
    font-size: var(--font-size-xl);
  }

  .search-container {
    flex-direction: column;
  }

  .search-btn {
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
  }

  .course-content {
    padding: 1.5rem;
  }

  .course-title {
    font-size: var(--font-size-lg);
  }

  .pagination-btn {
    padding: 0.75rem 1rem;
    min-width: 40px;
  }
}

/* Loading Spinner Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.course-card {
  animation: fadeIn 0.5s ease-out;
}

/* Hover Effects */
.course-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  opacity: 0;
  transition: var(--transition);
  border-radius: var(--border-radius-xl);
  z-index: -1;
}

.course-card:hover::before {
  opacity: 0.05;
}

/* Accessibility */
.course-card:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .navbar,
  .hero-section,
  .footer {
    display: none;
  }

  .courses-section {
    padding: 1rem 0;
  }

  .course-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid var(--gray-300);
  }
}
  